{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "有人异地组网", "version": "1.0.45"}, "tauri": {"systemTray": {"iconPath": "icons/icon-color-32x32.png", "iconAsTemplate": true}, "allowlist": {"fs": {"scope": ["$RESOURCE/*"]}, "all": true, "shell": {"all": false, "open": true}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"files": {"/usr/sbin/sdw-service": "target/release/sdw-service", "/usr/lib/systemd/system/sdw-service.service": "../resources-linux/sdw-service.service", "../control/rules": "../resources-linux/rules", "../control/postinst": "../resources-linux/postinst", "../control/prerm": "../resources-linux/prerm", "../control/postrm": "../resources-linux/postrm"}}, "targets": "all", "identifier": "com.sdw.to", "macOS": {"minimumSystemVersion": "10.13"}, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["usrTapTwo/", "wireguard_nt/"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "webviewInstallMode": {"type": "embed<PERSON><PERSON><PERSON><PERSON>"}, "wix": {"language": ["zh-CN"]}, "nsis": {"languages": ["SimpChinese"]}}}, "security": {"csp": "default-src 'self'; img-src 'self' https://trusted-domain.com data:; connect-src 'self' https://sd.sdw.usr.86.ltd wss://mq.sd.sdw.usr.86.ltd/mqtt; worker-src 'self' blob:;"}, "windows": [{"fullscreen": false, "resizable": true, "title": "有人异地组网", "width": 900, "height": 700, "visible": false}]}}