// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::env;
use std::ffi::c_void;
use std::fs;
use std::io::{Write};
use std::mem::size_of;
#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;
use std::path::PathBuf;
use std::process::{Command, Stdio,Child};
#[cfg(target_os = "windows")]
extern crate winreg;
use lazy_static::lazy_static;
use once_cell::sync::OnceCell;
#[cfg(not(target_os = "windows"))]
use sdw::service::proto::ClearInterfaeRequest;
use serde::{Serialize,Deserialize};
use systray_menu::CONNECT_MENU_ITEMS;
use tracing_subscriber::field::debug;
use std::sync::Mutex;
use tauri::{Manager, Window, SystemTray, SystemTrayEvent};
use tauri_plugin_autostart::MacosLauncher;
use tauri_plugin_window_state::{AppHandleExt, StateFlags};
#[cfg(target_os = "windows")]
use windows::{
    core::PCSTR,
    Win32::Foundation::{CloseHandle, GetLastError, HANDLE, INVALID_HANDLE_VALUE, WIN32_ERROR},
    Win32::System::JobObjects::{
        CreateJobObjectA, JobObjectExtendedLimitInformation,
        SetInformationJobObject, JOBOBJECT_BASIC_LIMIT_INFORMATION,
        JOBOBJECT_EXTENDED_LIMIT_INFORMATION, JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE,
    },
    Win32::System::Threading::GetCurrentProcessId,
};
#[cfg(target_os = "windows")]
use winreg::enums::*;
#[cfg(target_os = "windows")]
use winreg::RegKey;
use std::time::Duration;
use tauri::State;
use sysinfo::{Networks};
use std::thread;
use tauri::{AppHandle };
use ureq;
use std::io::{Read}; // 这里引入Read

use tokio::fs::File;
use tokio::io::AsyncWriteExt;

// 添加防火墙管理模块
#[cfg(target_os = "windows")]
mod firewall;

// 防火墙管理相关的Tauri命令
#[cfg(target_os = "windows")]
#[tauri::command]
async fn check_firewall_status() -> Result<String, String> {
    firewall::get_firewall_status()
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn is_firewall_enabled() -> Result<bool, String> {
    firewall::is_firewall_enabled()
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn configure_firewall_for_vpn(
    wireguard_port: u16,
    gretap_program_path: String,
    adapter_name: String,
) -> Result<String, String> {
    firewall::configure_wireguard_firewall_rules(
        wireguard_port,
        &gretap_program_path,
        &adapter_name,
    )?;
    Ok("防火墙规则配置成功".to_string())
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn cleanup_firewall_rules() -> Result<String, String> {
    firewall::cleanup_wireguard_firewall_rules()?;
    Ok("防火墙规则清理成功".to_string())
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn set_adapter_private(adapter_name: String) -> Result<String, String> {
    firewall::set_network_adapter_private(&adapter_name)?;
    Ok(format!("网络适配器 '{}' 已设置为私有网络", adapter_name))
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn force_fix_ping_firewall() -> Result<String, String> {
    firewall::force_configure_ping_firewall()?;
    Ok("强制防火墙配置完成，请重试ping测试".to_string())
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn diagnose_firewall() -> Result<String, String> {
    firewall::diagnose_firewall_issues()
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn nuclear_fix_firewall() -> Result<String, String> {
    firewall::nuclear_firewall_fix()?;
    Ok("核弹级防火墙修复完成！已采用最宽松设置，请立即测试ping连通性".to_string())
}

#[cfg(target_os = "windows")]
#[tauri::command]
async fn fix_windows10_icmp_blocking() -> Result<String, String> {
    firewall::fix_windows10_icmp_blocking()?;
    Ok("Windows 10 ICMP阻止问题修复完成".to_string())
}

// 非Windows系统的占位符命令
#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn force_fix_ping_firewall() -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn diagnose_firewall() -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn nuclear_fix_firewall() -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

// 非Windows系统的占位符命令
#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn check_firewall_status() -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn is_firewall_enabled() -> Result<bool, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn configure_firewall_for_vpn(
    _wireguard_port: u16,
    _gretap_program_path: String,
    _adapter_name: String,
) -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn cleanup_firewall_rules() -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn set_adapter_private(_adapter_name: String) -> Result<String, String> {
    Err("仅支持Windows系统".to_string())
}
use futures_util::StreamExt;
use tauri::CustomMenuItem;
use tauri::SystemTrayMenu;
use sdw::appstate::AppState;
#[cfg(target_os = "macos")]
use tauri::{api::process, Env};
// 使用 Mutex 保存子进程状态，以便在多个命令中访问
#[cfg(not(target_os = "windows"))]
use defguard_wireguard_rs::{
    Userspace,
    error::WireguardInterfaceError,
    host::{Host, Peer},
    key::Key,
    net::IpAddrMask,
    InterfaceConfiguration, WGApi, WireguardInterfaceApi,
};
use common::{find_free_tcp_port, get_interface_name};
// 在文件顶部添加 proto 相关导入
#[cfg(not(target_os = "windows"))]
use sdw::service::proto::{
    desktop_daemon_service_client::DesktopDaemonServiceClient,
    CreateInterfaceRequest,
    ReadInterfaceDataRequest,
    RemoveInterfaceRequest
};
use tonic::Code;
use log::{Level, LevelFilter,error,info,debug,trace};

use tauri_plugin_log::LogTarget;

struct VpnState {
    process: Option<Child>,
}
mod systray_menu;
// 隐藏窗口执行的标志 (Windows)
#[cfg(target_os = "windows")]
const CREATE_NO_WINDOW: u32 = 0x08000000;
#[cfg(target_os = "windows")]
use winapi::um::winuser::{ShowWindow, SW_HIDE};
#[cfg(target_os = "windows")]
use winapi::um::wincon::{GetConsoleWindow};

#[derive(Clone, serde::Serialize)]
struct Payload {
    args: Vec<String>,
    cwd: String,
}
#[cfg(target_os = "windows")]
#[derive(Debug)]
struct ChildProcessTracker {
    job_handle: HANDLE,
}

#[cfg(target_os = "windows")]
impl ChildProcessTracker {
    #[cfg(target_os = "windows")]
    fn new() -> Result<Self, WIN32_ERROR> {
        let job_name = format!("ChildProcessTracker{}\0", unsafe { GetCurrentProcessId() });
        let job_handle = match unsafe {
            CreateJobObjectA(None, PCSTR::from_raw(job_name.as_bytes().as_ptr()))
        } {
            Ok(handle) => handle,
            Err(_) => return Err(unsafe { GetLastError() }),
        };

        let job_object_info = JOBOBJECT_BASIC_LIMIT_INFORMATION {
            LimitFlags: JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE,
            ..Default::default()
        };

        let job_object_ext_info = JOBOBJECT_EXTENDED_LIMIT_INFORMATION {
            BasicLimitInformation: job_object_info,
            ..Default::default()
        };

        let result = unsafe {
            SetInformationJobObject(
                job_handle,
                JobObjectExtendedLimitInformation,
                &job_object_ext_info as *const JOBOBJECT_EXTENDED_LIMIT_INFORMATION
                    as *const c_void,
                size_of::<JOBOBJECT_EXTENDED_LIMIT_INFORMATION>() as u32,
            )
        };

        if result.as_bool() {
            Ok(Self { job_handle })
        } else {
            unsafe { CloseHandle(job_handle) };
            Err(unsafe { GetLastError() })
        }
    }
}
#[cfg(target_os = "windows")]
impl Drop for ChildProcessTracker {
    fn drop(&mut self) {
        if self.job_handle != INVALID_HANDLE_VALUE {
            unsafe { CloseHandle(self.job_handle) };
        }
    }
}

#[cfg(not(target_os = "windows"))]
#[derive(Debug)]
struct ChildProcessTracker;  // Empty struct for non-Windows
#[cfg(not(target_os = "windows"))]
impl ChildProcessTracker {
    pub fn new() -> Result<Self, ()> {
        Ok(Self)
    }

    pub fn add_process(&self, _process_handle: usize) -> Result<(), ()> {
        Ok(())
    }

    pub fn global() -> Option<&'static ChildProcessTracker> {
        None
    }
}
#[cfg(not(target_os = "windows"))]
impl Drop for ChildProcessTracker {
    fn drop(&mut self) {}
}

static CHILD_PROCESS_TRACKER: OnceCell<ChildProcessTracker> = OnceCell::new();

struct WiresockEnablingGuard;

impl Drop for WiresockEnablingGuard {
    fn drop(&mut self) {
        let mut state = WIRESOCK_STATE.lock().unwrap();
        state.wiresock_status = "STOPPED".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
    }
}

#[derive(Clone, Serialize, Debug)]
struct WiresockState {
    tunnel_id: String,
    wiresock_status: String,
    tunnel_status: String,
    logs: Vec<String>,
    ifname: Option<String>, // 新增接口名称存储字段
    endpoint: Option<String>, // 新增端点存储字段
}

impl WiresockState {
    fn new() -> Self {
        WiresockState {
            tunnel_id: String::new(),
            wiresock_status: "STOPPED".to_string(),
            tunnel_status: "DISCONNECTED".to_string(),
            logs: Vec::new(),
            ifname: None, // 初始化字段
            endpoint: None, // 初始化字段
        }
    }
}

mod tunnel;
use tunnel::Tunnel;
#[cfg(target_os = "windows")]
use wireguard_nt::{Wireguard, Adapter};
use ipnet::IpNet;
use tauri::Runtime;
use std::net::{SocketAddr, IpAddr};
lazy_static! {
    static ref WIRESOCK_STATE: Mutex<WiresockState> = Mutex::new(WiresockState::new());
}
#[cfg(target_os = "windows")]
lazy_static! {
    static ref ADAPTER_STATE: Mutex<Option<(Wireguard, Adapter)>> = Mutex::new(None);
}
#[cfg(target_os = "windows")]
// 密钥解析函数保持不变
fn parse_base64_key(key_str: &str) -> Result<[u8; 32], String> {
    base64::decode(key_str)
        .map_err(|e| format!("Base64解码失败: {}", e))
        .and_then(|bytes| {
            if bytes.len() != 32 {
                return Err("密钥长度必须为32字节".into());
            }
            let mut key = [0u8; 32];
            key.copy_from_slice(&bytes);
            Ok(key)
        })
}

// 修复后的DNS解析函数
use hickory_resolver::{
    config::{ResolverConfig, ResolverOpts, LookupIpStrategy},
    TokioAsyncResolver,
};

/// 异步解析端点到 SocketAddr（IPv4 优先）
// #[cfg(target_os = "windows")]
async fn resolve_socket_addr(endpoint: &str, port: &str) -> Result<SocketAddr, String> {
    // 解析端口号（支持 0-65535）
    let port: u16 = port
        .parse()
        .map_err(|_| format!("无效端口号: {}", port))?;

    // 优先尝试直接解析为 IP 地址
    if let Ok(ip) = endpoint.parse::<IpAddr>() {
        let ipv4 = match ip {
            IpAddr::V4(v4) => v4,
            IpAddr::V6(v6) => v6
                .to_ipv4()
                .ok_or_else(|| format!("仅支持 IPv4 地址: {}", endpoint))?,
        };
        return Ok(SocketAddr::new(IpAddr::V4(ipv4), port));
    }

    // 初始化 DNS 解析器（使用系统默认配置）
    let resolver = TokioAsyncResolver::tokio(
        ResolverConfig::default(),
        {
            let mut opts = ResolverOpts::default();
            opts.ip_strategy = LookupIpStrategy::Ipv4Only;
            opts
        },
    );

    // 带超时的 DNS 查询（5 秒）
    let lookup = tokio::time::timeout(Duration::from_secs(5), resolver.lookup_ip(endpoint))
        .await
        .map_err(|_| format!("DNS 查询超时: {}", endpoint))?
        .map_err(|e| format!("DNS 查询失败: {}", e))?;

    // 选择第一个 IPv4 地址
    lookup
        .iter()
        .find(|ip| ip.is_ipv4())
        .map(|ip| SocketAddr::new(ip, port))
        .ok_or_else(|| format!("未找到 IPv4 地址: {}", endpoint))
}
// 开启三层组网
#[cfg(target_os = "windows")]
#[tauri::command]
async fn enable_wiresock(
    tunnel: Tunnel,
    _log_level: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    // 状态检查
    {
        let state = WIRESOCK_STATE.lock().unwrap();
        if state.wiresock_status != "STOPPED" {
            return Err("enable_wiresock is already running".into());
        }
    }

    // 更新状态
    update_state(&app_handle, |state| {
        state.tunnel_id = tunnel.id.clone();
        state.wiresock_status = "STARTING".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs = Vec::new();
    });

    // 解析AllowedIPs
    let allowed_ips: Vec<IpNet> = tunnel.rules.allowed.ipAddresses
        .split(',')
        .map(|s| s.trim().parse())
        .collect::<Result<_, _>>()
        .map_err(|e| format!("IP解析错误: {}", e))?;

    // 解析Endpoint
    let endpoint = resolve_socket_addr(
        &tunnel.peer.endpoint,
        &tunnel.peer.port.to_string() // 确保端口转换为字符串
    ).await?;

    // 构建wireguard配置
    let interface = wireguard_nt::SetInterface {
        listen_port: tunnel.interface.port.parse().ok(),
        private_key: Some(parse_base64_key(&tunnel.interface.privateKey)?),
        public_key: None,
        peers: vec![wireguard_nt::SetPeer {
            public_key: Some(parse_base64_key(&tunnel.peer.publicKey)?),
            preshared_key: None,
            keep_alive: tunnel.peer.persistentKeepalive.parse().ok(),
            allowed_ips,
            endpoint,
        }],
    };
            println!("{}",tunnel.interface.ipv4Address);
    // 加载驱动和创建适配器
  let wireguard = unsafe { wireguard_nt::load_from_path("wireguard_nt/bin/amd64/wireguard.dll") }
        .map_err(|e| format!("驱动加载失败: {}", e))?;

    let adapter = Adapter::create(&wireguard, "usrTwo", "WireGuard", None)
        .or_else(|_| Adapter::open(&wireguard, "usrTwo"))
        .map_err(|e| format!("适配器操作失败: {}", e))?;

    // 应用配置
    adapter.set_config(&interface)
        .map_err(|e| format!("配置应用失败: {},{}", tunnel.interface.ipv4Address,e))?;

    let internal_ip: IpNet = tunnel.interface.ipv4Address.parse()
        .map_err(|e| format!("接口IP解析失败: {}", e))?;

    adapter.set_default_route(&[internal_ip], &interface)
        .map_err(|e| format!("路由设置失败: {}", e))?;

    // 启动适配器
    adapter.up()
        .map_err(|e| format!("适配器启动失败: {}", e))?;

    // 保存状态
    ADAPTER_STATE.lock().unwrap().replace((wireguard, adapter));

    // 更新状态
    update_state(&app_handle, |state| {
        state.wiresock_status = "RUNNING".to_string();
        state.tunnel_status = "CONNECTED".to_string();
    });

    Ok(())
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn enable_wiresock(
    tunnel: Tunnel,
    _log_level: String,
    app_handle: tauri::AppHandle,
)-> Result<(), String> {
    let ifname = get_interface_name("");
    //打印ifname
    print!("ifname: {}", ifname);

     // 状态检查
    use std::str::FromStr;
     {
        let state = WIRESOCK_STATE.lock().unwrap();
        if state.wiresock_status != "STOPPED" {
            return Err("enable_wiresock is already running".into());
        }
    }

    // 更新状态
    update_state(&app_handle, |state| {
        state.tunnel_id = tunnel.id.clone();
        state.wiresock_status = "STARTING".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs = Vec::new();
    });
    let state = app_handle.state::<AppState>();
    info!("原始公钥值: {}", tunnel.peer.publicKey);
    let peer_key = Key::from_str(&tunnel.peer.publicKey)
        .map_err(|e| format!("解析公钥失败: {} (输入值: {})", e, tunnel.peer.publicKey))?;
    let mut peer = Peer::new(peer_key);

    // 解析Endpoint
    let endpoint: SocketAddr = resolve_socket_addr(
        &tunnel.peer.endpoint,
        &tunnel.peer.port.to_string() // 确保端口转换为字符串
    ).await?;

    let endpoint_str = format!("{}:{}", endpoint.ip(), endpoint.port());
    //打印
    info!("Endpoint: {}", endpoint_str);
    peer.set_endpoint(&endpoint_str)
       .map_err(|e| format!("设置端点失败: {}", e))?;
    peer.persistent_keepalive_interval = tunnel.peer.persistentKeepalive.parse().ok();

    // let peer_psk = Key::from_str(&tunnel.peer.presharedKey)?;
    // peer.preshared_key = Some(peer_psk);
    // 解析AllowedIPs 逗号分割tunnel.rules.allowed.ipAddresses，得到String类型的数组
    let allowed_ips: Vec<String> = tunnel.rules.allowed.ipAddresses
    .split(',')
    .map(|s| s.trim().to_string()) // 添加 to_string() 转换
    .collect();
    for allowed_ip in &allowed_ips {
        match IpAddrMask::from_str(allowed_ip.trim()) {
            Ok(addr) => {
                peer.allowed_ips.push(addr);
            }
            Err(err) => {
                // Handle the error from IpAddrMask::from_str, if needed
                //打印
                println!("Error parsing IP address {allowed_ip}: {err}");
                // Continue to the next iteration of the loop
                continue;
            }
        }
    }

    let port = match find_free_tcp_port() {
        Some(p) => p,
        None => return Err("无法获取可用端口".into())
    };

    let internal_ip = IpAddrMask::from_str(&tunnel.interface.ipv4Address).unwrap();

    let config: InterfaceConfiguration = InterfaceConfiguration {
        name: ifname.clone(),
        prvkey: tunnel.interface.privateKey.clone(),
        addresses: vec![internal_ip],
        port: port.into(),
        peers: vec![peer.clone()],
        mtu: tunnel.interface.mtu.parse().ok(),
    };
    let request = CreateInterfaceRequest {
        config: Some(config.clone().into()),
        allowed_ips,
        dns: if tunnel.interface.dns.is_empty() {
            None
        } else {
            Some(tunnel.interface.dns.clone())
        },
    };
    let mut client = state.client.clone();
    if let Err(error) = client.create_interface(request).await {
        // 打印错误信息
        println!("Error: {:?}", error);
        if error.code() == Code::Unavailable {
            error!(
                "Failed to set up connection for ifname {ifname}; background service is \
                unavailable. Make sure the service is running. Error: {error}, Interface \
                configuration: {config:?}"
            );
        } else {
            error!(
                "Failed to send a request to the background service to create an interface for \
                ifname {ifname} with the following configuration: {config:?}. \
                Error: {error}"
            );
        }
        return Err(format!("创建接口失败: {}", error));
    }else{
        println!("创建接口成功");
    }
      // 更新状态
    update_state(&app_handle, |state| {
        state.wiresock_status = "RUNNING".to_string();
        state.tunnel_status = "CONNECTED".to_string();
        state.ifname = Some(ifname.clone()); // 存储接口名称
        state.endpoint = Some(endpoint_str.clone()); // 存储端点
    });
    Ok(())
}

// 断开二层组网
#[cfg(target_os = "windows")]
#[tauri::command]
async fn disable_wiresock( app_handle: tauri::AppHandle) -> Result<(), String> {
    let mut adapter_state = ADAPTER_STATE.lock().unwrap();
    if let Some((_wireguard, adapter)) = adapter_state.take() {
        // 关闭适配器
        adapter.down()
            .map_err(|e| format!("关闭失败: {}", e))?;
    }
    update_state(&app_handle, |state| {
        state.wiresock_status = "STOPPED".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs.push("Tunnel disabled normally".into());
    });
    Ok(())
}
#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn disable_wiresock( app_handle: tauri::AppHandle) -> Result<(), String> {
    info!("disable_wiresock called");
    let ifname: String = {
        let state = WIRESOCK_STATE.lock().unwrap();
        state.ifname.clone().ok_or("未找到活动接口".to_string())?
    };
    let endpoints = {
        let state = WIRESOCK_STATE.lock().unwrap();
        state.endpoint.clone().ok_or("未找到端点".to_string())?
    };
    let state: State<'_, AppState> = app_handle.state::<AppState>();
    let mut client = state.client.clone();
    let request = RemoveInterfaceRequest {
        interface_name: ifname.clone(),
        endpoint: endpoints.clone(),
    };
    if let Err(error) = client.remove_interface(request).await {
        let msg = if error.code() == Code::Unavailable {
            format!(
                "Couldn't remove interface {}. Background service is unavailable. \
                Please make sure the service is running. Error: {error}.",
                ifname
            )
        } else {
            format!(
                "Failed to send a request to the background service to remove interface \
                {}. Error: {error}.",
                ifname
            )
        };
        error!("{}", msg);
        // return Err(msg);
    }
    //删除完成把state.ifname state.endpoints 清掉
    update_state(&app_handle, |state| {
        state.wiresock_status = "STOPPED".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs.push("Tunnel disabled normally".into());
        state.ifname = None; // 清空接口名称
        state.endpoint = None; // 清空端点
    });

    Ok(())
}


#[tauri::command]
fn change_icon(app_handle: tauri::AppHandle, enabled: bool) {
    #[cfg(not(target_os = "macos"))]
    {
        if enabled {
            app_handle
                .tray_handle()
                .set_icon(tauri::Icon::Raw(
                    include_bytes!("assets/icons/icon-enabled.ico").to_vec(),
                ))
                .unwrap();
        } else {
            app_handle
                .tray_handle()
                .set_icon(tauri::Icon::Raw(
                    include_bytes!("assets/icons/icon-default.ico").to_vec(),
                ))
                .unwrap();
        }
    }
    // #[cfg(target_os = "macos")]
    // {
    //     if enabled {
    //         app_handle
    //         .tray_handle()
    //         .set_icon(tauri::Icon::Raw(
    //             include_bytes!("assets/icons/icon-gray-32x32.png").to_vec(),
    //         ))
    //         .unwrap();
    //     }else{
    //         app_handle
    //         .tray_handle()
    //         .set_icon(tauri::Icon::Raw(
    //             include_bytes!("assets/icons/icon-white-32x32.png").to_vec(),
    //         ))
    //         .unwrap();
    //     }
    // }

}

#[tauri::command]
fn change_systray_tooltip(app_handle: tauri::AppHandle, tooltip: String) {
    let _ = app_handle.tray_handle().set_tooltip(&tooltip);
}

#[tauri::command]
fn add_or_update_systray_menu_item(
    app_handle: tauri::AppHandle,
    item_id: String,
    item_label: String,
) {
    systray_menu::add_or_update_systray_menu_item(&app_handle, item_id, item_label);
}

#[tauri::command]
fn update_systray_connect_menu_items(app_handle: tauri::AppHandle, items: Vec<(String, String)>) {
    systray_menu::update_systray_connect_menu_items(&app_handle, items);
}

#[tauri::command]
fn remove_systray_menu_item(app_handle: tauri::AppHandle, item_id: String) {
    systray_menu::remove_systray_menu_item(&app_handle, item_id);
}
#[tauri::command]
fn get_os_type() -> &'static str {
    #[cfg(target_os = "windows")]
    {
        "windows"
    }
    #[cfg(target_os = "macos")]
    {
        // 根据CPU架构细分macOS平台
        #[cfg(target_arch = "aarch64")]
        {
            "macos-aarch64" // Apple Silicon (M1/M2等)
        }
        #[cfg(target_arch = "x86_64")]
        {
            "macos-x86_64" // Intel芯片
        }
    }
    #[cfg(target_os = "linux")]
    {
        "linux"
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        "unknown"
    }
}

fn update_state<F>(app_handle: &tauri::AppHandle, update: F)
where
    F: FnOnce(&mut WiresockState),
{
    let mut state = WIRESOCK_STATE.lock().unwrap();
    update(&mut state);
    app_handle.emit_all("wiresock_state", &*state).unwrap();
}
// 获取wiresock状态
#[tauri::command]
fn get_wiresock_state(app_handle: tauri::AppHandle) -> Result<(), String> {
    let state = WIRESOCK_STATE.lock().unwrap();
    app_handle.emit_all("wiresock_state", &*state).unwrap();
    Ok(())
}
// 获取tapUSr网卡版本
#[tauri::command]
fn get_tap_usr_version() -> Result<String, String> {
    #[cfg(target_os = "windows")] {
        let uninstall_keys = RegKey::predef(HKEY_LOCAL_MACHINE)
            .open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall")
            .map_err(|e| e.to_string())?;

        for name in uninstall_keys.enum_keys().map(|x| x.unwrap()) {
            let subkey = uninstall_keys
                .open_subkey(&name)
                .map_err(|e| e.to_string())?;
            match subkey.get_value::<String, _>("DisplayName") {
                Ok(display_name) => {
                    if display_name.starts_with("TAP-Windows") {
                        match subkey.get_value::<String, _>("DisplayVersion") {
                            Ok(version) => {
                                println!("Installed TAP-USR-Windows Version: {}", version);
                                return Ok(version);
                            }
                            Err(e) => eprintln!("Error getting display version: {:?}", e),
                        }
                    }
                }
                Err(_) => (),
            }
        }

        Ok("TAP-USR-Windows_not_installed".into())
    }
    #[cfg(not(target_os = "windows"))]
        return Err("仅支持Windows系统".into());
}

// 安装tap_usr
#[cfg(target_os = "windows")]
#[tauri::command]
async fn install_tap_usr() -> Result<String, String> {
    // 获取当前目录
    let current_dir = env::current_dir().unwrap();
    // 构建WireSock安装程序的路径
    let tap_usr_path = &mut current_dir.into_os_string().into_string().unwrap();
    tap_usr_path.push_str(r#"\usrTapTwo\tap-windows-9.24.7-I601-Win10.exe"#);

    // 使用PowerShell启动msiexec以便获取退出码以判断是否成功安装
    let arg = format!(
        "$process = Start-Process -FilePath '{}' -ArgumentList '/S' -Wait -PassThru -Verb RunAs; $process.ExitCode",
        tap_usr_path
    );

    // 启动WireSock安装程序（静默模式）
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &arg,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // 隐藏窗口
        .spawn()
        .expect("Failed to start PowerShell");
 // 获取 PowerShell 窗口句柄
    let hwnd: isize = unsafe { GetConsoleWindow() as isize };
    if hwnd != 0 {
        // 隐藏窗口
        unsafe {
            ShowWindow(hwnd as *mut _, SW_HIDE);
        }
    }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = String::from_utf8_lossy(&output.stdout).to_string();
    let stderr = String::from_utf8_lossy(&output.stderr).to_string();

    if !output.status.success() {
        return Err(format!("Error: {}\n{}", stderr, stdout));
    }

    // 将输出行转换为JSON字符串
    let output_lines = stdout.lines().collect::<Vec<_>>();
    let output_json = serde_json::to_string(&output_lines).unwrap_or_else(|_| "[]".to_string());

    Ok(output_json)
}

// 修改非 Windows 平台的定义，添加 async 和正确的返回类型
#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn install_tap_usr() -> Result<String, String> {
    Err("仅支持Windows系统".into())
}
#[tauri::command]
async fn show_app(window: Window) {
    // Show main window
    println!("Showing the main window");
    window
        .get_window("main")
        .expect("no window labeled 'main' found")
        .show()
        .unwrap();
}

lazy_static! {
    static ref MINIMIZE_TO_TRAY: Mutex<bool> = Mutex::new(true);
}

#[tauri::command]
fn set_minimize_to_tray(value: bool) {
    let mut minimize = MINIMIZE_TO_TRAY.lock().unwrap();
    *minimize = value;
}

lazy_static! {
    static ref LOG_LIMIT: Mutex<i32> = Mutex::new(50);
}

#[tauri::command]
fn set_log_limit(value: String) {
    let mut loglim = LOG_LIMIT.lock().unwrap();
    *loglim = value.parse::<i32>().unwrap();
}



// 三层组网保活
#[cfg(target_os = "windows")]
#[tauri::command]
fn three_active_ping(ip: String) -> Result<(), String> {
    // 构建命令
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &format!("ping {}", ip),
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // 隐藏初始进程窗口
        .spawn()
        .expect("Failed to start PowerShell");
 // 获取 PowerShell 窗口句柄
    let hwnd: isize = unsafe { GetConsoleWindow() as isize };
    if hwnd != 0 {
        // 隐藏窗口
        unsafe {
            ShowWindow(hwnd as *mut _, SW_HIDE);
        }
    }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = String::from_utf8_lossy(&output.stdout).to_string();
    let stderr = String::from_utf8_lossy(&output.stderr).to_string();

    if !output.status.success() {
        return Err(format!("Error: {}\n{}", stderr, stdout));
    }

    Ok(())
}


#[cfg(not(target_os = "windows"))]
#[tauri::command]
fn three_active_ping(ip: String) -> Result<(), String> {
    // 构建命令
    //不执行ping 逻辑
    //打印ip
    debug!("ip: {}", ip);
    Ok(())
    // let status = Command::new("sh")
    //                         .arg("-c")
    //                         .arg(format!("ping {}", ip))
    //                         .status();
    // match status {
    //     Ok(_) => Ok(()),
    //     Err(e) => Err(format!("执行失败: {}", e)),
    // }
}
// 运行powershell命令
#[cfg(target_os = "windows")]
#[tauri::command]
fn run_powershell(command: String) -> Result<String, String> {
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &command,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW)
        .spawn()
        .map_err(|e| format!("Failed to run PowerShell: {}", e))?;
 // 获取 PowerShell 窗口句柄
    let hwnd: isize = unsafe { GetConsoleWindow() as isize };
    if hwnd != 0 {
        // 隐藏窗口
        unsafe {
            ShowWindow(hwnd as *mut _, SW_HIDE);
        }
    }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = String::from_utf8_lossy(&output.stdout).to_string();
    let stderr = String::from_utf8_lossy(&output.stderr).to_string();

    if !output.status.success() {
        return Err(stderr);
    }

    Ok(stdout)
}
#[cfg(not(target_os = "windows"))]
#[tauri::command]
fn run_powershell(command: String) -> Result<String, String> {
    Err("仅支持Windows系统".into())
}

use std::fs::OpenOptions;
use std::io;
use std::path::Path;
// 修改edge.conf
#[cfg(target_os = "windows")]
fn modify_edge_conf(
    n3n_dir: &Path,
    filter: bool,
    mac: String,
    community: String,
    supernode: String,
    key: String,
    ip: String,
    limit: String,
) -> io::Result<()> {
    // Create or open the configuration file
    let conf_path = n3n_dir.join("edge.conf.tmp");
    let mut file = OpenOptions::new()
        .create(true)    // 如果不存在则创建
        .write(true)     // 可写
        .truncate(true)  // 清空文件内容（关键修改）
        .open(&conf_path)?;

    // Write the fixed configuration sections first
    writeln!(file, "[tuntap]")?;
    writeln!(file, "name=usr")?;
    writeln!(file, "address={}",ip)?;
    writeln!(file, "address_mode=static\n")?;

    writeln!(file, "[community]")?;
    writeln!(file, "name={}", community)?;
    writeln!(file, "key={}", key)?;
    writeln!(file, "header_encryption=true")?;
    writeln!(file, "cipher=xor")?;
    writeln!(file, "supernode={}\n", supernode)?;

    writeln!(file, "[connection]")?;
    writeln!(file, "allow_p2p=true")?;
    writeln!(file, "connect_tcp=false\n")?;
    writeln!(file, "description=${}$\n",limit)?;

    writeln!(file, "[daemon]")?;
    writeln!(file, "background=false\n")?;

    writeln!(file, "[logging]")?;
    writeln!(file, "verbose=2\n")?;

    writeln!(file, "[filter]")?;
    writeln!(file, "allow_multicast=true")?;
    writeln!(file, "allow_routing=true\n")?;
    // Write the configuration lines
    writeln!(file, "allow_noncenter_filter={}", filter)?;
    // Only write the mac if it's not empty
    if !mac.is_empty() {
        writeln!(file, "center_mac={}", mac)?;
    }
    // Ensure all data is written to disk
    file.flush()?;
    println!(
        "edge.conf 文件已修改 - allow_noncenter_filter: {}, center_mac: {}",
        filter, mac
    );

    Ok(())
}
#[cfg(target_os = "windows")]
fn convert_file(n3n_dir: &Path) -> io::Result<()> {
    // 获取配置文件路径
    let conf_path = n3n_dir.join("edge.conf.tmp");
    // 检查文件是否存在
    if !conf_path.exists() {
        return Err(io::Error::new(
            io::ErrorKind::NotFound,
            "edge.conf  文件不存在"
        ));
    }
    // 获取当前工作目录
    let current_dir = env::current_dir()?;
    // 构建加密工具路径
    let enc_path = current_dir.join("usrTapTwo").join("file-enc-test.exe");
    // 检查加密工具是否存在
    if !enc_path.exists() {
        return Err(io::Error::new(
            io::ErrorKind::NotFound,
            "加密工具 file-enc-test.exe  不存在"
        ));
    }
    // 构建输出文件路径
    let output_path = n3n_dir.join("edge.conf");
    // 构建完整的 PowerShell 命令
    let command = format!(
        "& '{}' '{}' '{}'",
        enc_path.display(),
        conf_path.display(),
        output_path.display()
    );
    // 执行命令
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &command,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // CREATE_NO_WINDOW
        .spawn();
    match cmd {
        Ok( child) => {
            // 读取输出
            let output = child.wait_with_output().expect("Failed to wait for PowerShell");
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            // 检查命令是否成功
            if !output.status.success() {
                return Err(io::Error::new(
                    io::ErrorKind::Other,
                    format!("文件加密失败: {}\n{}", stderr, stdout)
                ));
            }
            // 加密成功后删除原始文件
            fs::remove_file(&conf_path)?;
            println!("edge.conf  文件已加密为 edge.bin  并删除原始文件");
            Ok(())
        }
        Err(e) => Err(io::Error::new(
            io::ErrorKind::Other,
            format!("无法启动 PowerShell: {}", e)
        )),
    }
}

/**
 * 新建一个start_vpn
 * 去除n3n相关代码，只保留最新的gretap
 * 先获取wg的配置，启动
 * 启动后获取二层的数据
 * 然后启动gretap
 */

// 开启二层组网
#[cfg(target_os = "windows")]
#[tauri::command]
async fn start_vpn(
    tunnel: Tunnel,
    ip: String,
    netmask: String,
    gretapmtu: String,
    localip: String,
    remoteip: String,
    vpnstate: State<'_, Mutex<VpnState>>,
    app_handle: tauri::AppHandle,
) -> Result<String, String>{

    // 状态检查
    {
        let state = WIRESOCK_STATE.lock().unwrap();
        if state.wiresock_status != "STOPPED" {
            return Err("enable_wiresock is already running".into());
        }
    }

    // 更新状态
    update_state(&app_handle, |state| {
        state.tunnel_id = tunnel.id.clone();
        state.wiresock_status = "STARTING".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs = Vec::new();
    });

    // 解析AllowedIPs
    let allowed_ips: Vec<IpNet> = tunnel.rules.allowed.ipAddresses
        .split(',')
        .map(|s| s.trim().parse())
        .collect::<Result<_, _>>()
        .map_err(|e| format!("IP解析错误: {}", e))?;

    // 解析Endpoint
    let endpoint = resolve_socket_addr(
        &tunnel.peer.endpoint,
        &tunnel.peer.port.to_string() // 确保端口转换为字符串
    ).await?;

    // 构建wireguard配置
    let interface = wireguard_nt::SetInterface {
        listen_port: tunnel.interface.port.parse().ok(),
        private_key: Some(parse_base64_key(&tunnel.interface.privateKey)?),
        public_key: None,
        peers: vec![wireguard_nt::SetPeer {
            public_key: Some(parse_base64_key(&tunnel.peer.publicKey)?),
            preshared_key: None,
            keep_alive: tunnel.peer.persistentKeepalive.parse().ok(),
            allowed_ips,
            endpoint,
        }],
    };
    println!("{}",tunnel.interface.ipv4Address);
    // 加载驱动和创建适配器
    let wireguard = unsafe { wireguard_nt::load_from_path("wireguard_nt/bin/amd64/wireguard.dll") }
        .map_err(|e| format!("驱动加载失败: {}", e))?;

    let adapter = Adapter::create(&wireguard, "usrTwo", "WireGuard", None)
        .or_else(|_| Adapter::open(&wireguard, "usrTwo"))
        .map_err(|e| format!("适配器操作失败: {}", e))?;

    // 应用配置
    adapter.set_config(&interface)
        .map_err(|e| format!("配置应用失败: {},{}", tunnel.interface.ipv4Address,e))?;

    let internal_ip: IpNet = tunnel.interface.ipv4Address.parse()
        .map_err(|e| format!("接口IP解析失败: {}", e))?;

    adapter.set_default_route(&[internal_ip], &interface)
        .map_err(|e| format!("路由设置失败: {}", e))?;

    // 启动适配器
    adapter.up()
        .map_err(|e| format!("适配器启动失败: {}", e))?;

    // 保存状态
    ADAPTER_STATE.lock().unwrap().replace((wireguard, adapter));

    // 配置WireGuard虚拟网卡的防火墙规则
    #[cfg(target_os = "windows")]
    {
        println!("配置WireGuard虚拟网卡防火墙规则...");

        // 设置虚拟网卡为私有网络
        if let Err(e) = firewall::set_network_adapter_private("usrTwo") {
            println!("警告: 设置网络适配器为私有失败: {}", e);
        }

        // 只为WireGuard虚拟网卡配置ICMP规则
        if let Err(e) = firewall::configure_wireguard_adapter_firewall("usrTwo") {
            println!("警告: WireGuard适配器防火墙配置失败: {}", e);
        } else {
            println!("WireGuard适配器防火墙配置完成");
        }

        // 专门修复Win10防火墙阻止ICMP的问题
        println!("应用Win10 ICMP阻止修复...");
        if let Err(e) = firewall::fix_windows10_icmp_blocking() {
            println!("警告: Win10 ICMP阻止修复失败: {}", e);
        } else {
            println!("Win10 ICMP阻止修复完成");
        }
    }

    // 获取可变的 VPN 状态
    let mut vpn_state = vpnstate.lock().map_err(|e| format!("状态锁定失败: {}", e))?;
 // 获取当前时间戳（格式：YYYYMMDD-HHMMSS）
//     let timestamp = Local::now().format("%Y%m%d-%H%M%S").to_string();
    // 检查是否已有进程运行
    if vpn_state.process.is_some() {
        return Ok("VPN 已经运行".to_string());
    }

    // 获取用户主目录
    let home_dir = dirs::home_dir().ok_or("无法获取用户主目录")?;
    let usrtap_dir = home_dir.join("usrtap");
    fs::create_dir_all(&usrtap_dir).map_err(|e| format!("目录创建失败: {}", e))?;

    // 获取当前工作目录
    let current_dir = env::current_dir().map_err(|e| format!("获取当前目录失败: {}", e))?;

    // 生成log的文件名
    let log_filename = format!("usrtap.log");
    let log_file = usrtap_dir.join(&log_filename);
    // 创建并打开新日志文件
    let log_file: fs::File = OpenOptions::new()
        .create(true)
        .append(true)
        .open(&log_file)
        .map_err(|e| format!("无法创建日志文件: {}", e))?;
    // 启动usr-gretap.exe 并重定向输出到日志文件
    let gretap_path = current_dir.join("usrTapTwo").join("usr-gretap.exe");
    let child = Command::new(gretap_path)
        .args(&[
            ip,
            netmask,
            gretapmtu,
            "static".to_string(),
            localip,
            remoteip
        ])
        .stdout(log_file.try_clone().map_err(|e| format!("无法克隆日志文件: {}", e))?)
        .stderr(log_file)  // 错误日志也写入同一文件
        .creation_flags(CREATE_NO_WINDOW)  // Windows 隐藏窗口标志
        .spawn()
        .map_err(|e| format!("启动失败: {}", e))?;

    // 保存子进程句柄
    vpn_state.process = Some(child);
      // 更新状态
    update_state(&app_handle, |state| {
        state.wiresock_status = "RUNNING".to_string();
        state.tunnel_status = "CONNECTED".to_string();
    });

    Ok("VPN 启动成功，日志已存储到 ~/usrtap/usrtap.log".to_string())
    
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn start_vpn(
    tunnel: Tunnel,
    ip: String,
    netmask: String,
    mtu: String,
    localip: String,
    remoteip: String,
    vpnstate: State<'_, Mutex<VpnState>>,
    app_handle: tauri::AppHandle,
) -> Result<String, String>{
    Err("仅支持Windows系统".into())
}
// 关闭二层组网
#[cfg(target_os = "windows")]
#[tauri::command]
fn stop_vpn(state: State<'_, Mutex<VpnState>>) -> Result<String, String> {
    let mut vpn_state = state.lock().map_err(|e| format!("状态锁定失败: {}", e))?;
    // 修复点：添加 mut 声明
    if let Some(mut child) = vpn_state.process.take() {
        child.kill().map_err(|e| format!("终止进程失败: {}", e))?;
        child.wait().map_err(|e| format!("等待进程结束失败: {}", e))?;

        // 清理防火墙规则
        #[cfg(target_os = "windows")]
        {
            if let Err(e) = firewall::cleanup_wireguard_firewall_rules() {
                println!("警告: 防火墙规则清理失败: {}", e);
                // 不返回错误，因为VPN已经停止
            }
        }

        Ok("VPN 已停止".to_string())
    } else {
        Err("VPN 未运行".to_string())
    }
}
#[cfg(not(target_os = "windows"))]
#[tauri::command]
fn stop_vpn(state: State<'_, Mutex<VpnState>>) -> Result<String, String> {
    Err("仅支持Windows系统".into())
}
// 检查二层组网状态
#[cfg(target_os = "windows")]
#[tauri::command]
fn check_vpn_status(state: State<'_, Mutex<VpnState>>) -> Result<String, String> {
    // 获取可变的 VPN 状态
    let mut vpn_state = state.lock().map_err(|e| format!("状态锁定失败: {}", e))?;
    match &mut vpn_state.process {
        Some(child) => {
            // 检查进程状态
            match child.try_wait() {
                Ok(None) => Ok("Running".to_string()),
                Ok(Some(status)) => {
                    // 清除已退出的进程状态
                    vpn_state.process = None;
                    Ok(format!("VPN 已停止，退出状态: {}", status))
                }
                Err(e) => Err(format!("状态检查失败: {}", e)),
            }
        }
        None => Ok("Not Running".to_string()),
    }
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
fn check_vpn_status(state: State<'_, Mutex<VpnState>>) -> Result<String, String> {
    Err("仅支持Windows系统".into())
}

// 返回结果的结构体
#[derive(Serialize, Deserialize)]
struct CmdResult {
    success: bool,
    output: String,
    error: Option<String>,
}

// 定义可序列化的返回结构
#[derive(Serialize)]
pub struct CmdResultA {
    success: bool,
    output: String,
    error: Option<String>,
}

// 重置二层组网网卡
#[cfg(target_os = "windows")]
#[tauri::command]
fn reset_network(adapter: String) -> CmdResultA {
    // 组合命令：删除IP地址 -> 禁用网卡 -> 启用网卡
    let combined_command = format!(
        "Get-NetIPAddress -InterfaceAlias {} | Remove-NetIPAddress  -Confirm:$false;
         netsh interface set interface name={} admin=disable ;
         netsh interface set interface name={} admin=enable",
        adapter, adapter, adapter
    );
    // 执行组合命令
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &combined_command,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // CREATE_NO_WINDOW
        .spawn();
         // 获取 PowerShell 窗口句柄
            let hwnd: isize = unsafe { GetConsoleWindow() as isize };
            if hwnd != 0 {
                // 隐藏窗口
                unsafe {
                    ShowWindow(hwnd as *mut _, SW_HIDE);
                }
            }
    match cmd {
        Ok( child) => {
            // 读取输出
            let output = child.wait_with_output().expect("Failed to wait for PowerShell");
            let _stdout = String::from_utf8_lossy(&output.stdout).to_string();
            let _stderr = String::from_utf8_lossy(&output.stderr).to_string();
            // 构建输出信息
            let mut output_buffer = String::new();
            append_output(&mut output_buffer, "组合命令", &output);
            CmdResultA {
                success: output.status.success(),
                output: output_buffer,
                error: if output.status.success() {
                    None
                } else {
                    Some("部分操作失败，请检查输出".to_string())
                },
            }
        }
        Err(e) => CmdResultA {
            success: false,
            output: String::new(),
            error: Some(format!("命令执行失败: {}", e)),
        },
    }
}

#[cfg(not(target_os = "windows"))]
#[tauri::command]
fn reset_network(adapter: String) -> CmdResultA {
    CmdResultA {
        success: false,
        output: String::new(),
        error: Some("仅支持Windows系统".to_string()),
    }
}

// 辅助函数：收集命令输出
#[cfg(target_os = "windows")]
fn append_output(buffer: &mut String, step: &str, output: &std::process::Output) {
    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);
    buffer.push_str(&format!("[{}]\n", step));
    buffer.push_str(&format!("退出码: {}\n", output.status));
    buffer.push_str(&format!("标准输出:\n{}\n", stdout));
    buffer.push_str(&format!("错误输出:\n{}\n", stderr));
    buffer.push_str("\n--------------------\n");
}

use encoding_rs::GBK;
use regex::Regex;
use std::{collections::HashMap, error::Error};

// 内部解析用结构体
#[cfg(target_os = "windows")]
#[derive(Debug)]
struct AdapterInfo {
    description: String,
    mac_address: String,
    ipv4_addresses: Vec<String>,
}
// 添加 Clone 派生以支持 Tauri 的 IPC 序列化
#[derive(Debug, Clone, Serialize)]
pub struct NetworkAdapter {
    pub name: String,
     pub mac: String,
    pub ipv4_addresses: Vec<String>,
    pub description: String,
}
// 修改后的 DTO 结构（供前端使用）
#[derive(Debug, Clone, Serialize)]
pub struct NetworkAdapterDTO {
    pub name: String,
    pub mac: String,
    pub description: String,
    pub ipv4: String, // 改为单个字符串字段
}
// 获取所有网卡
#[tauri::command]
async fn get_network_adapters_all() -> Result<Vec<NetworkAdapterDTO>, String> {
    // 获取原始数据
    let raw_adapters = internal_get_adapters().map_err(|e| e.to_string())?;

    // 转换为 DTO
    let adapters = raw_adapters
        .into_iter()
        .map(|adapter| NetworkAdapterDTO {
            name: adapter.name,
            mac: adapter.mac,
            description: adapter.description,
            ipv4: adapter.ipv4_addresses.first() // 取第一个 IPv4
                .cloned()
                .unwrap_or_default(), // 若无则空字符串
        })
        .collect();
    Ok(adapters)
}
// 核心函数：执行命令并返回适配器信息列表
#[cfg(target_os = "windows")]
fn internal_get_adapters() -> Result<Vec<NetworkAdapter>, Box<dyn Error>> {
    // 1. 执行 ipconfig 命令
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            "ipconfig /all",
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW)
        .spawn()
        .expect("Failed to start PowerShell");
 // 获取 PowerShell 窗口句柄
    let hwnd: isize = unsafe { GetConsoleWindow() as isize };
    if hwnd != 0 {
        // 隐藏窗口
        unsafe {
            ShowWindow(hwnd as *mut _, SW_HIDE);
        }
    }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = &output.stdout;

    // 2. 处理 GBK 编码
    let (cow, _, _) = GBK.decode(stdout);
    let output_str = cow.into_owned();

    // 3. 解析原始输出
    let raw_adapters = parse_ipconfig_output(&output_str);
    // 4. 转换为前端需要的结构
    Ok(raw_adapters
        .into_iter()
        .map(|(name, info)| NetworkAdapter {
            name,
            mac: info.mac_address,
            ipv4_addresses: info.ipv4_addresses,
            description: info.description,
        })
        .collect())
}

#[cfg(not(target_os = "windows"))]
fn internal_get_adapters() -> Result<Vec<NetworkAdapter>, Box<dyn Error>> {
    //不是Windows返回空
    Ok(Vec::new())
}
// 增强版解析函数（支持中英文
#[cfg(target_os = "windows")]
fn parse_ipconfig_output(output: &str) -> HashMap<String, AdapterInfo> {
    let mut adapters = HashMap::new();

    // 多语言正则表达式（匹配中英文关键词）
    let adapter_re = Regex::new(r"(?i)(适配器|adapter)\s+(.*?):").unwrap();
    let description_re = Regex::new(r"(?i)(描述|Description)[ .:：]+(.*)").unwrap();
    let mac_re = Regex::new(r"(?i)(物理地址|Physical Address)[ .:：]+([0-9A-Fa-f-:]+)").unwrap();
    let ipv4_re = Regex::new(r"(?i)(IPv4 地址|IPv4 Address)[ .:：]+([\d.]+)").unwrap();

    // 当前适配器信息
    let mut current_adapter = String::new();
    let mut current_description = String::new();
    let mut current_mac = String::new();
    let mut current_ipv4 = Vec::new();

    for line in output.lines() {
        // 1. 处理适配器名称
        if let Some(caps) = adapter_re.captures(line) {
            // 保存前一个适配器
            if !current_adapter.is_empty() {
                save_adapter(&mut adapters, &current_adapter, &current_description,
                           &current_mac, &current_ipv4);
            }

            // 提取适配器名称（中文取第二个分组，英文也取第二个分组）
            current_adapter = caps.get(2).unwrap().as_str().trim().to_string();
            reset_current(&mut current_description, &mut current_mac, &mut current_ipv4);
        }

        // 2. 处理描述
        else if let Some(caps) = description_re.captures(line) {
            current_description = caps.get(2).unwrap().as_str().trim().to_string();
        }

        // 3. 处理MAC地址
        else if let Some(caps) = mac_re.captures(line) {
            current_mac = normalize_mac(caps.get(2).unwrap().as_str());
        }

        // 4. 处理IPv4地址
        else if let Some(caps) = ipv4_re.captures(line) {
            if let Some(ip) = parse_ipv4(caps.get(2).unwrap().as_str()) {
                current_ipv4.push(ip);
            }
        }
    }

    // 保存最后一个适配器
    if !current_adapter.is_empty() {
        save_adapter(&mut adapters, &current_adapter, &current_description,
                   &current_mac, &current_ipv4);
    }

    adapters
}
// --- 辅助函数 ---
#[cfg(target_os = "windows")]
fn reset_current(desc: &mut String, mac: &mut String, ipv4: &mut Vec<String>) {
    desc.clear();
    mac.clear();
    ipv4.clear();
}
#[cfg(target_os = "windows")]
fn save_adapter(adapters: &mut HashMap<String, AdapterInfo>,
                name: &str, desc: &str, mac: &str, ipv4: &[String]) {
    adapters.insert(
        name.to_string(),
        AdapterInfo {
            description: desc.to_string(),
            mac_address: mac.to_string(),
            ipv4_addresses: ipv4.to_vec(),
        },
    );
}

// MAC地址格式化（统一为XX-XX-XX-XX-XX-XX）
#[cfg(target_os = "windows")]
fn normalize_mac(raw: &str) -> String {
    raw.replace(':', "-")
       .to_uppercase()
}

// 提取纯净的IPv4地址（去除括号内容）
#[cfg(target_os = "windows")]
fn parse_ipv4(raw: &str) -> Option<String> {
    raw.split(|c| c == '(' || c == ')')
       .next()
       .map(|s| s.trim().to_string())
}
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::time::{Instant};
use std::thread::JoinHandle;

// 流量监控状态（共享线程安全）
struct TrafficMonitor {
    handle: Option<JoinHandle<()>>, // 监控线程句柄
    running: Arc<AtomicBool>,        // 运行状态标志
    prev_data: Arc<Mutex<Option<(u64, u64, Instant)>>>, // 上次数据 (下载总量, 上传总量, 时间)
}
const MAX_SPEED: u64 = 1073741824; // 1 GB/s（1024 * 1024 * 1024）

// 流量监控封装
impl TrafficMonitor {
       fn new() -> Self {
            Self {
                handle: None,
                running: Arc::new(AtomicBool::new(false)),
                prev_data: Arc::new(Mutex::new(None)),
            }
        }
    // 启动监控
    fn start<R: Runtime>(&mut self, app: tauri::AppHandle<R>, interface: String) {
        if self.running.load(Ordering::Relaxed) {
            return;
        }
        self.running.store(true, Ordering::Relaxed);

        let running = self.running.clone();
        let prev_data = self.prev_data.clone();

        self.handle = Some(thread::spawn(move || {
            let mut networks = Networks::new_with_refreshed_list();
            let mut prev_down = 0;
            let mut prev_up = 0;
            let mut prev_time = Instant::now();
             // 分钟统计变量
             let mut minute_start_time = Instant::now();
             let mut minute_start_down = 0;
             let mut minute_start_up = 0;

            // 初始化首次数据
            if let Some(data) = networks.get(&interface) {
                prev_down = data.total_received();
                prev_up = data.total_transmitted();
                prev_time = Instant::now();
                *prev_data.lock().unwrap() = Some((prev_down, prev_up, prev_time));
                // 初始化分钟统计基准
                minute_start_time = prev_time;
                minute_start_down = prev_down;
                minute_start_up = prev_up;
                                       app.emit_all(
                                           "traffic_update",
                                           serde_json::json!({
                                               "current_up": 0,
                                               "current_down": 0,
                                               "down_speed": 0,
                                               "up_speed": 0
                                           })
                                       ).unwrap();
            }

            // 监控循环
            while running.load(Ordering::Relaxed) {
                thread::sleep(Duration::from_secs(10));
                 if !running.load(Ordering::Relaxed) {
                        break; // 确保在停止后立即退出循环
                 }
                networks.refresh(false);

                if let Some(data) = networks.get(&interface) {
                    let current_down = data.total_received();
                    let current_up = data.total_transmitted();
                    let current_time = Instant::now();
                    // 计算增量速度
                    let duration = current_time.duration_since(prev_time).as_secs_f64();
                   let raw_down_speed = ((current_down - prev_down) as f64 / duration).round() as u64;
                   let raw_up_speed = ((current_up - prev_up) as f64 / duration).round() as u64;

                   // 应用阈值过滤（关键代码）
                   let down_speed = if raw_down_speed > MAX_SPEED {
                       eprintln!("WARN: Abnormal download speed: {} B/s", raw_down_speed);
                       0  // 置零或保持上次有效值
                   } else {
                       raw_down_speed
                   };

                   let up_speed = if raw_up_speed > MAX_SPEED {
                       eprintln!("WARN: Abnormal upload speed: {} B/s", raw_up_speed);
                       0  // 置零或保持上次有效值
                   } else {
                       raw_up_speed
                   };
                                       // 更新数据
                                       prev_down = current_down;
                                       prev_up = current_up;
                                       prev_time = current_time;
                                       *prev_data.lock().unwrap() = Some((prev_down, prev_up, prev_time));

                                       // 发送事件到前端
                                       app.emit_all(
                                           "traffic_update",
                                           serde_json::json!({
                                               "current_up": current_up,
                                               "current_down": current_down,
                                               "down_speed": down_speed,
                                               "up_speed": up_speed
                                           })
                                       ).unwrap();
                                   // 每分钟统计上报
                                   if current_time.duration_since(minute_start_time).as_secs() >= 60 {
                                       let tx = current_up - minute_start_up;
                                       let rx = current_down - minute_start_down;
                                       app.emit_all(
                                           "minute_report",
                                           serde_json::json!({
                                               "tx": tx,
                                               "rx": rx,
                                               "up": tx/60,    // 转换为b/s
                                               "down": rx/60 // 转换为b/s
                                           })
                                       ).unwrap();
                                       // 重置基准值
                                       minute_start_time = current_time;
                                       minute_start_down = current_down;
                                       minute_start_up = current_up;
                                   }

                }
            }
        }));
    }

    // 停止监控
    fn stop(&mut self) {
        self.running.store(false, Ordering::Relaxed);
        if let Some(handle) = self.handle.take() {
            handle.join().unwrap();
        }
    }
}

// 开启二层监控
#[tauri::command]
fn start_monitor<R: Runtime>(
    app: tauri::AppHandle<R>,
    interface: String,
    state: tauri::State<'_, Arc<Mutex<TrafficMonitor>>>,
) {
    state.lock().unwrap().start(app, interface);
}
// 停止二层监控
#[tauri::command]
fn stop_monitor(state: tauri::State<'_, Arc<Mutex<TrafficMonitor>>>) {
    state.lock().unwrap().stop();
}

#[derive(Clone, serde::Serialize)]
struct DownloadProgress {
    received: u64,
    total: Option<u64>,
    done: bool,
}
#[derive(Clone, serde::Serialize)]
struct Debug {
debug: String,
}
#[derive(serde::Deserialize)]
struct DownloadRequest {
    url: String,
    file_name: String,
}

// 下载文件并发送进度事件
#[tauri::command]
async fn download_file_update(
    app: AppHandle,
    request: DownloadRequest,
) -> Result<PathBuf, String> {
    //判断当前系统，如果是windows request.file_name增加后缀.ext 如果是mac request.file_name增加后缀.pkg
    let mut file_name = request.file_name.clone();
    if cfg!(target_os = "windows") {
        file_name = format!("{}.{}", request.file_name, "exe");
    }
    if cfg!(target_os = "macos") {
        file_name = format!("{}.{}", request.file_name, "pkg");
    }
    if cfg!(target_os = "linux") {
        file_name = format!("{}.{}", request.file_name, "deb");
    }
    app.emit_all("debug", Debug {
        debug: "开始执行".to_string(),
    }).unwrap();

    let response = ureq::get(&request.url)
    .call()
    .map_err(|e| format!("请求失败: {}", e))?;
    if response.status().is_success(){
        let total_size = response
            .headers()
            .get("content-length")
            .and_then(|hv| hv.to_str().ok()?.parse::<u64>().ok())
            .unwrap_or(0);
        app.emit_all("debug", Debug {
            debug: format!("total_size: {}", total_size),
        }).unwrap();

        let downloads_dir = app.path_resolver().app_data_dir()
        .ok_or("无法获取下载目录".to_string())?;
        let file_path = downloads_dir.join(&file_name);
        let mut file = File::create(&file_path).await
            .map_err(|e| e.to_string())?;
        app.emit_all("debug", Debug {
            debug: "创建文件".to_string(),
        }).unwrap();

        let mut reader = response.into_body().into_reader();
        let mut received: u64 = 0;
        let mut buffer = [0u8; 8192];

        loop {
            let bytes_read = reader.read(&mut buffer).map_err(|e| e.to_string())?;
            if bytes_read == 0 {
                break;
            }
            file.write_all(&buffer[..bytes_read]).await.map_err(|e| e.to_string())?;
            file.flush().await.map_err(|e| e.to_string())?;
            received += bytes_read as u64;
            app.emit_all("download_progress", DownloadProgress {
                received,
                total: Some(total_size),
                done: false,
            }).unwrap();
        }
        app.emit_all("download_progress", DownloadProgress {
            received,
            total: Some(total_size),
            done: true,
        }).unwrap();


        Ok(file_path)
    }else {
        return Err(format!("请求失败: {}", response.status()));
    }

}

// 更新安装软件
#[cfg(target_os = "windows")]
#[tauri::command]
async fn install_package_update(path: PathBuf) -> Result<String, String> {
    // 处理路径格式化和转义
    let path_str = path
        .canonicalize()
        .map_err(|e| format!("路径解析失败: {}", e))?
        .display()  // 转换为可格式化的 Display 对象
        .to_string()
        .replace('"', "\"\"");  // 处理双引号转义
    // 构建 PowerShell 命令 (增加路径引号处理)
    let arg = format!(
        "$process = Start-Process -FilePath \"{}\" -ArgumentList '/S' -Wait -PassThru -Verb RunAs; $process.ExitCode",
        path_str
    );
    // 执行命令并等待输出
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-Command",
            &arg,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // CREATE_NO_WINDOW
        .spawn()
        .map_err(|e| format!("启动 PowerShell 失败: {}", e))?;
         // 获取 PowerShell 窗口句柄
            let hwnd: isize = unsafe { GetConsoleWindow() as isize };
            if hwnd != 0 {
                // 隐藏窗口
                unsafe {
                    ShowWindow(hwnd as *mut _, SW_HIDE);
                }
            }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = String::from_utf8_lossy(&output.stdout).to_string();
    let stderr = String::from_utf8_lossy(&output.stderr).to_string();
    if !output.status.success() {
        return Err(format!("安装失败: {}", stderr));
    }
    // 解析标准输出为 JSON
    let output_json = serde_json::json!({
        "exit_code": output.status.code().unwrap_or(-1),
        "output": stdout.lines().collect::<Vec<_>>()
    });
    Ok(output_json.to_string())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn install_package_update(app: AppHandle,path: PathBuf) -> Result<String, String> {
    debug!("install_package_update");
    // // 处理路径格式化和转义
    let path_str = path
       .canonicalize()
       .map_err(|e| format!("路径解析失败: {}", e))?
       .display()  // 转换为可格式化的 Display 对象
       .to_string()
       .replace('"', "\"\"");  // 处理双引号转义
    // 下载成功后打开目录
    // 执行 macOS 打开目录命令
    Command::new("open")
       .arg(&path_str)
       .spawn()
       .map_err(|e| format!("打开目录失败: {}", e))?;
    // 改为异步方式退出
    install_quit(&app).await;
    Ok(path_str.to_string())
}

#[cfg(target_os = "linux")]
#[tauri::command]
async fn install_package_update(app: AppHandle,path: PathBuf) -> Result<String, String> {
    use std::process::Command;

    let path = path.canonicalize().map_err(|e| format!("路径解析失败: {}", e))?;
    debug!("install_package_update path: {}", path.to_str().unwrap());
    // 用 apt 安装，自动升级和补依赖
    let status = Command::new("pkexec")
        .arg("apt")
        .arg("install")
        .arg("-y")
        .arg(path.to_str().unwrap())
        .status()
        .map_err(|e| format!("安装命令启动失败: {}", e))?;

    if !status.success() {
        return Err(format!("安装失败，退出码: {}", status));
    }
    // 改为异步方式退出
    install_quit(&app).await;
    Ok(format!("安装成功: {}", path.display()))
}

// 浏览器打开
#[tauri::command]
fn open_browser(url: String) -> Result<(), String> {
    open::that(&url)
        .map_err(|e| format!("打开失败: {}", e))?;
    Ok(())
}
#[tauri::command]
fn get_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}
// 全局 token 存储
lazy_static! {
    static ref TOKEN: Mutex<Option<String>> = Mutex::new(None);
}
// 存储token，exit使用
#[tauri::command]
fn store_token(token: String) {
    let mut token_lock = TOKEN.lock().unwrap();
    *token_lock = Some(token);
    println!("Token stored");
}

// 获取 token 并发送退出请求的函数
async fn send_logout_request() -> Result<(), Box<dyn std::error::Error>> {
    let token = TOKEN.lock().unwrap().clone();

    if let Some(token) = token {
        //正式环境
        let url = "https://api-sdw.usr.cn/client/signOut";
        //rc环境
        // let url = "https://sd.sdw.usr.86.ltd/client/signOut";
           // 压力测试环境
        //  let url = "https://rc2.sdw.86.ltd/client/signOut";

        debug!("请求URL: {}", url);

        // 使用 ureq 发送 POST 请求
        let response = ureq::post(url)
            .header("content-type", "application/json; charset=utf-8")
            .header("authorization", &token)
            .send_empty();

        match response {
            Ok(resp) => {
                if resp.status() == 200 {
                    debug!("登出成功");
                } else {
                    debug!("服务器返回错误状态码: {}", resp.status());
                }
            }
            Err(e) => { // 处理网络错误
                debug!("网络错误: {}", e);
            }
        }
    }
    Ok(())
}

// 响应数据结构体调整为泛型
#[derive(Debug, Serialize)]
struct ResponseData<T> {
    data: Option<T>,
    error: Option<String>,
}

// 网络接口数据结构体
#[derive(Debug, Serialize, Deserialize, PartialEq)]
pub struct NetworkInterface {
    pub ip_address: String,
    pub operation_mode: String,
    pub mac_address: String,
}
#[cfg(target_os = "windows")]
#[tauri::command]
async fn get_edges() -> ResponseData<Vec<NetworkInterface>> { // 修改返回类型
    // PowerShell 核心命令
    let ps_script = format!(
        r#"try {{
            $response = Invoke-RestMethod -Uri 'http://localhost:5644/v1' `
                -Method Post `
                -Headers @{{"Content-Type"="application/json"}} `
                -Body '{{"jsonrpc": "2.0", "method": "get_edges", "id": 1, "params": null}}' `
                -ErrorAction Stop

            $response.result | ConvertTo-Json -Compress
        }} catch {{
            $_.Exception.Message | ConvertTo-Json
        }}"#
    );

    // 执行命令并等待输出
    let cmd = Command::new("powershell.exe")
        .args(&[
            "-NoProfile",
            "-NonInteractive",
            "-WindowStyle",
            "Hidden",
            "-ExecutionPolicy",
            "Bypass",
            "-Command",
            &ps_script,
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .creation_flags(CREATE_NO_WINDOW) // CREATE_NO_WINDOW - stop a command window showing
        .spawn()
        .expect("Failed to start PowerShell");
 // 获取 PowerShell 窗口句柄
    let hwnd: isize = unsafe { GetConsoleWindow() as isize };
    if hwnd != 0 {
        // 隐藏窗口
        unsafe {
            ShowWindow(hwnd as *mut _, SW_HIDE);
        }
    }
    // 读取输出
    let output = cmd.wait_with_output().expect("Failed to wait for PowerShell");
    let stdout = String::from_utf8_lossy(&output.stdout).to_string();
    let stderr = String::from_utf8_lossy(&output.stderr).to_string();

    if !output.status.success() {
        return ResponseData {
            data: None,
            error: Some(format!("PowerShell执行错误: {}", stderr)),
        };
    }

    // 使用自定义解析器处理响应
    match parse_network_interfaces(&stdout) {
        Ok(interfaces) => ResponseData {
            data: Some(interfaces),
            error: None,
        },
        Err(e) => ResponseData {
            data: None,
            error: Some(e),
        },
    }
}
#[cfg(not(target_os = "windows"))]
#[tauri::command]
async fn get_edges() -> ResponseData<Vec<NetworkInterface>> {
    ResponseData {
        data: None,
        error: Some("get_edges 仅支持 Windows 平台".to_string()),
    }
}
// 增强型解析器
pub fn parse_network_interfaces(raw_output: &str) -> Result<Vec<NetworkInterface>, String> {
    // 首先验证原始数据有效性
    if raw_output.trim().is_empty() {
        return Err("空响应数据".into());
    }

    // 解析为JSON值
    let parsed: serde_json::Value = serde_json::from_str(raw_output)
        .map_err(|e| format!("JSON解析失败: {}", e))?;

    match parsed {
        serde_json::Value::Array(arr) => {
            let mut interfaces = Vec::with_capacity(arr.len());

            for (index, value) in arr.into_iter().enumerate() {
                let obj = value.as_object()
                    .ok_or_else(|| format!("第{}个元素不是对象", index + 1))?;

                // 解析IP地址（允许空值）
                let ip = obj.get("ip4addr")
                    .and_then(|v| v.as_str())
                    .unwrap_or("N/A")
                    .to_string();

                // 解析运行模式（允许空值）
             let mode = obj.get("mode")
                    .and_then(|v| v.as_str())
                     .unwrap_or("N/A")
                    .to_string();
                // 解析MAC地址（允许空值）
                let mac = obj.get("macaddr")
                    .and_then(|v| v.as_str())
                     .unwrap_or("N/A")
                    .to_string();

                // 验证MAC地址格式
                if !is_valid_mac(&mac) {
                    return Err(format!("无效的MAC地址格式: {}", mac));
                }

                interfaces.push(NetworkInterface {
                    ip_address: ip,
                    operation_mode: mode,
                    mac_address: mac
                });
            }

            Ok(interfaces)
        }
        _ => Err("根元素必须是数组".to_string())
    }
}

// 增强型MAC地址验证
fn is_valid_mac(mac: &str) -> bool {
    let re = Regex::new(r"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$").unwrap();
    re.is_match(mac)
}
//安装退出
async fn install_quit(app: &tauri::AppHandle){
if let Err(e) = send_logout_request().await {
        error!("Failed to send logout request: {}", e);
    }
    debug!("installQuit...");
    let vpn_state = app.state::<Mutex<VpnState>>();
    let traffic_monitor = app.state::<Arc<Mutex<TrafficMonitor>>>();

    // 停止 VPN
    if let Err(e) = stop_vpn(tauri::State::from(vpn_state)) {
        error!("Failed to stop VPN: {}", e);
    }

    // 停止流量监控
    traffic_monitor.lock().unwrap().stop();

    // 禁用 WireSock
    if let Err(e) = disable_wiresock(app.app_handle()).await {
        error!("Failed to disable WireSock: {}", e);
    }

    // 获取并重置最后设置的静态IP适配器
    let app_state = app.state::<AppState>();
    if let Some(adapter) = app_state.last_static_ip_adapter.lock().unwrap().take() {
        let _ = reset_network(adapter);
    }

    // Save window state to disk
    let _ = app.save_window_state(StateFlags::all());

    // Exit the app
    app.exit(0);
}
fn quit(app: &tauri::AppHandle){
    // Minimize the app window
    // if let Some(window) = app.get_window("main") {
    //      window.hide().unwrap();
    // };
    // 创建运行时用于异步操作
    // 使用 Tauri 的异步运行时
    tauri::async_runtime::block_on(async {
        if let Err(e) = send_logout_request().await {
            error!("Failed to send logout request: {}", e);
        }
        // 异步处理其他操作
        async {
            debug!("Quitting...");
            let vpn_state = app.state::<Mutex<VpnState>>();
            let traffic_monitor = app.state::<Arc<Mutex<TrafficMonitor>>>();

            // 停止 VPN
            if let Err(e) = stop_vpn(tauri::State::from(vpn_state)) {
                error!("Failed to stop VPN: {}", e);
            }

            // 停止流量监控
            traffic_monitor.lock().unwrap().stop();

            // 禁用 WireSock
            if let Err(e) = disable_wiresock(app.app_handle()).await {
                error!("Failed to disable WireSock: {}", e);
            }

            // 获取并重置最后设置的静态IP适配器
            let app_state = app.state::<AppState>();
            if let Some(adapter) = app_state.last_static_ip_adapter.lock().unwrap().take() {
                let _ = reset_network(adapter);
            }

            // Save window state to disk
            let _ = app.save_window_state(StateFlags::all());

            // Exit the app
            app.exit(0);
        }.await
    });
}




// 检查服务状态的内部函数
#[cfg(target_os = "macos")]
fn check_service_status() -> Result<bool, String> {
    use std::process::Command;

    // 检查 launchctl list
    let list_output = Command::new("launchctl")
        .arg("list")
        .arg("usr.sdw")
        .output()
        .map_err(|e| format!("Failed to check service status: {}", e))?;

    let list_stdout = String::from_utf8_lossy(&list_output.stdout);

    // 检查进程是否运行
    let ps_output = Command::new("ps")
        .arg("aux")
        .output()
        .map_err(|e| format!("Failed to check running processes: {}", e))?;

    let ps_stdout = String::from_utf8_lossy(&ps_output.stdout);
    let sdw_service_running = ps_stdout.contains("sdw-service");

    // 如果在 launchctl 中找到或者进程在运行，认为服务已加载
    if (list_output.status.success() && list_stdout.contains("usr.sdw")) || sdw_service_running {
        Ok(true)
    } else {
        Ok(false)
    }
}

// 启动时加载 LaunchDaemon 服务
#[cfg(target_os = "macos")]
fn load_launch_daemon() -> Result<(), String> {
    use std::process::Command;

    // 首先检查服务是否已经加载，避免不必要的密码请求
    if check_service_status()? {
        return Ok(());
    }

    // 检查 plist 文件是否存在
    let plist_path = "/Library/LaunchDaemons/usr.sdw.plist";
    if !std::path::Path::new(plist_path).exists() {
        return Err(format!("LaunchDaemon plist file not found at: {}", plist_path));
    }


    // 服务未加载，使用 osascript 请求管理员权限并执行命令
    // 使用 -w 参数确保服务被启用并设置为自启动
    let script = r#"
        do shell script "launchctl load -w /Library/LaunchDaemons/usr.sdw.plist" with administrator privileges
    "#;

    let output = Command::new("osascript")
        .arg("-e")
        .arg(script)
        .output()
        .map_err(|e| format!("Failed to execute osascript: {}", e))?;

    if output.status.success() {
        Ok(())
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        // 如果服务已经加载，不算错误
        if stderr.contains("already loaded") || stderr.contains("Operation already in progress") {
            Ok(())
        } else {
            Err(format!("Failed to load LaunchDaemon: {}", stderr))
        }
    }
}

#[cfg(not(target_os = "macos"))]
fn load_launch_daemon() -> Result<(), String> {
    // 非 macOS 系统不需要加载 LaunchDaemon
    Ok(())
}

// 提供给前端调用的命令
#[tauri::command]
async fn load_launch_daemon_service() -> Result<String, String> {
    load_launch_daemon().map(|_| "LaunchDaemon service loaded successfully".to_string())
}

// 检查 LaunchDaemon 服务状态
#[cfg(target_os = "macos")]
#[tauri::command]
async fn check_launch_daemon_status() -> Result<String, String> {
    use std::process::Command;

    // 方法1: 检查服务是否在 launchctl list 中
    let list_output = Command::new("launchctl")
        .arg("list")
        .arg("usr.sdw")
        .output()
        .map_err(|e| format!("Failed to check service status: {}", e))?;

    let list_stdout = String::from_utf8_lossy(&list_output.stdout);
    let list_stderr = String::from_utf8_lossy(&list_output.stderr);

    // 方法2: 检查进程是否在运行
    let ps_output = Command::new("ps")
        .arg("aux")
        .output()
        .map_err(|e| format!("Failed to check running processes: {}", e))?;

    let ps_stdout = String::from_utf8_lossy(&ps_output.stdout);
    let sdw_service_running = ps_stdout.contains("sdw-service");

    // 方法3: 检查 plist 文件是否存在且已加载
    let plist_exists = std::path::Path::new("/Library/LaunchDaemons/usr.sdw.plist").exists();

    // 综合判断服务状态
    if list_output.status.success() && list_stdout.contains("usr.sdw") {
        // 解析 launchctl list 的输出
        let lines: Vec<&str> = list_stdout.lines().collect();
        if let Some(line) = lines.first() {
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 3 {
                let pid = parts[0];
                let status = parts[1];
                if pid != "-" && pid != "0" {
                    Ok(format!("Service is loaded and running (PID: {})", pid))
                } else if status == "0" {
                    if sdw_service_running {
                        Ok("Service is loaded and running (detected via ps)".to_string())
                    } else {
                        Ok("Service is loaded but not running".to_string())
                    }
                } else {
                    Ok(format!("Service is loaded with status: {}", status))
                }
            } else {
                if sdw_service_running {
                    Ok("Service is loaded and running (detected via ps)".to_string())
                } else {
                    Ok("Service is loaded but status unclear".to_string())
                }
            }
        } else {
            if sdw_service_running {
                Ok("Service is loaded and running (detected via ps)".to_string())
            } else {
                Ok("Service is loaded but status unclear".to_string())
            }
        }
    } else if sdw_service_running {
        // launchctl 没找到，但进程在运行
        Ok("Service is running but not managed by launchctl".to_string())
    } else {
        println!("❌ Service not found in launchctl list and not running");
        Ok("Service is not loaded".to_string())
    }
}

#[cfg(not(target_os = "macos"))]
#[tauri::command]
async fn check_launch_daemon_status() -> Result<String, String> {
    Ok("LaunchDaemon not supported on this platform".to_string())
}

// 调试命令：直接返回 launchctl 的原始输出
#[cfg(target_os = "macos")]
#[tauri::command]
async fn debug_launchctl_output() -> Result<String, String> {
    use std::process::Command;

    let output = Command::new("launchctl")
        .arg("list")
        .arg("usr.sdw")
        .output()
        .map_err(|e| format!("Failed to execute launchctl: {}", e))?;

    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);

    Ok(format!(
        "Exit status: {}\nStdout: '{}'\nStderr: '{}'",
        output.status,
        stdout,
        stderr
    ))
}

#[cfg(not(target_os = "macos"))]
#[tauri::command]
async fn debug_launchctl_output() -> Result<String, String> {
    Ok("LaunchDaemon not supported on this platform".to_string())
}

// 检查服务是否被禁用（disabled）
#[cfg(target_os = "macos")]
#[tauri::command]
async fn check_launch_daemon_disabled() -> Result<String, String> {
    use std::process::Command;

    let output = Command::new("launchctl")
        .arg("print-disabled")
        .arg("system")
        .output()
        .map_err(|e| format!("Failed to check disabled services: {}", e))?;

    let stdout = String::from_utf8_lossy(&output.stdout);

    if stdout.contains("\"usr.sdw\" => true") {
        Ok("Service is disabled".to_string())
    } else if stdout.contains("\"usr.sdw\" => false") {
        Ok("Service is enabled".to_string())
    } else {
        Ok("Service status unknown".to_string())
    }
}

#[cfg(not(target_os = "macos"))]
#[tauri::command]
async fn check_launch_daemon_disabled() -> Result<String, String> {
    Ok("LaunchDaemon not supported on this platform".to_string())
}
fn main() {
    // 初始化 Tokio 运行时 (关键修复)
    let rt = tokio::runtime::Runtime::new().expect("无法创建 Tokio 运行时");
    let _enter = rt.enter();
     // 添加 panic 捕获（关键调试信息）
    std::panic::set_hook(Box::new(|panic_info| {
        error!("程序异常退出: {}", panic_info);
    }));
     // add bundled `wireguard-go` binary to PATH
     #[cfg(target_os = "macos")]
     {
         let current_bin_path =
             process::current_binary(&Env::default()).expect("Failed to get current binary path");
         let current_bin_dir = current_bin_path
             .parent()
             .expect("Failed to get current binary directory");
         let current_path = env::var("PATH").expect("Failed to get current PATH variable");
         env::set_var(
             "PATH",
             format!("{current_path}:{}", current_bin_dir.to_str().unwrap()),
         );
     }

    // Initialize global job object
    if let Ok(child_process_tracker) = ChildProcessTracker::new() {
        CHILD_PROCESS_TRACKER.set(child_process_tracker).ok();
    }

    let app = tauri::Builder::default()
        .manage(AppState::new()) // 添加状态管理
        .manage(Mutex::new(VpnState { process: None }))
        .manage(Arc::new(Mutex::new(TrafficMonitor::new())))
        // 添加 Tokio 运行时集成 (关键配置)
        .setup(|app| {
            // 新增日志插件配置 ▼▼▼

            // Sets the time format. Service's logs have a subsecond part, so we also need to include it here,
            // otherwise the logs couldn't be sorted correctly when displayed together in the UI.
            let format = time::macros::format_description!(
                "[[[year]-[month]-[day]][[[hour]:[minute]:[second].[subsecond]]"
            );
            let app_handle = app.handle();
            app_handle.plugin(
                tauri_plugin_log::Builder::default()
                    .format(move |out, message, record| {
                        out.finish(format_args!(
                            "{}[{}][{}] {}",
                            tauri_plugin_log::TimezoneStrategy::UseUtc
                                .get_now()
                                .format(&format)
                                .unwrap(),
                            record.level(),
                            record.target(),
                            message
                        ))
                    })
                    .targets([LogTarget::LogDir, LogTarget::Stdout, LogTarget::Webview])
                    .level(LevelFilter::Debug)
                    .filter(|metadata| {
                        if metadata.level() == Level::Error {
                            return true;
                        }
                        if !["sdw", "tauri"].iter().any(|t| metadata.target().contains(t)) {
                            return false;
                        }
                        true
                    })
                    .build(),
            ).unwrap();
            #[cfg(not(target_os = "windows"))]{
                let state = app_handle.state::<AppState>();
                //调用client.clear_interface
                let mut client = state.client.clone();
                let request = ClearInterfaeRequest {};

                // Use Tauri's async runtime to execute async code
                tauri::async_runtime::block_on(async {
                    if let Err(e) = client.clear_interfae(request).await {
                        error!("Failed to clear interfaces: {}", e);
                    } else {
                        info!("Successfully cleared residual interfaces");
                    }
                });
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            run_powershell,start_vpn, stop_vpn, check_vpn_status,reset_network,get_network_adapters_all,start_monitor, stop_monitor,
            install_package_update,download_file_update,open_browser,get_version,store_token,get_edges,
            three_active_ping,
            enable_wiresock,
            disable_wiresock,
            install_tap_usr,
            get_wiresock_state,
            get_tap_usr_version,
            show_app,
            set_minimize_to_tray,
            set_log_limit,
            change_icon,
            change_systray_tooltip,
            add_or_update_systray_menu_item,
            update_systray_connect_menu_items,
            remove_systray_menu_item,
            get_os_type,
            load_launch_daemon_service,
            check_launch_daemon_status,
            // 防火墙管理命令
            check_firewall_status,
            is_firewall_enabled,
            configure_firewall_for_vpn,
            cleanup_firewall_rules,
            set_adapter_private,
            force_fix_ping_firewall,
            diagnose_firewall,
            nuclear_fix_firewall,
            fix_windows10_icmp_blocking,
        ])
        .on_window_event(|event| {
            if let tauri::WindowEvent::CloseRequested { api, .. } = event.event() {
                debug!("CloseRequested...");
                #[cfg(target_os = "windows")]
                let _ = event.window().hide();

                #[cfg(target_os = "macos")]
                let _ = tauri::AppHandle::hide(&event.window().app_handle());

                #[cfg(not(target_os = "linux"))]
                api.prevent_close();
            }
        })
       .system_tray(
           SystemTray::new()
               .with_tooltip("TunnlTo: Disconnected")
               .with_menu(
                   SystemTrayMenu::new()
                       // 添加最小化菜单项
                       .add_item(CustomMenuItem::new("minimize", "最小化到托盘"))
                       // 添加退出菜单项
                       .add_item(CustomMenuItem::new("exit", "退出程序"))
               )
       )
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                if let Some(window) = app.get_window("main") {
                    window.show().unwrap();
                    window.unminimize().unwrap();
                    match window.set_focus() {
                        Ok(_) => println!("Window focus set successfully."),
                        Err(e) => println!("Failed to set window focus: {:?}", e),
                    }
                };
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                let _connect_menu_items = CONNECT_MENU_ITEMS.lock().unwrap();
                match id.as_str() {
                    "minimize" => {
                        // Hide the app window
                        if let Some(window) = app.get_window("main") {
                            #[cfg(target_os = "linux")]
                            window.minimize().unwrap(); // Linux使用最小化

                            #[cfg(not(target_os = "linux"))]
                            window.hide().unwrap(); // 非Linux系统保持隐藏
                        };
                    }
                    "exit" => {
                        debug!("Menu item click exiting...");
                        quit(app);
                    }
                    _ => todo!()
                }
            }
            _ => {}
        })
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .plugin(tauri_plugin_single_instance::init(|app, argv, cwd| {
            println!("{}, {argv:?}, {cwd}", app.package_info().name);
            app.emit_all("single-instance", Payload { args: argv, cwd })
                .unwrap();
        }))
        .plugin(tauri_plugin_autostart::init(
            MacosLauncher::LaunchAgent,
            Some(vec!["--flag1", "--flag2"]), /* arbitrary number of args to pass to your app */
        ))
        // 新增日志插件配置 ▲▲▲
        .build(tauri::generate_context!())
        .expect("error while running tauri application");

        // Handle Ctrl-C
        debug!("Setting up Ctrl-C handler...");
        tauri::async_runtime::spawn(async move {
            tokio::signal::ctrl_c()
                .await
                .expect("Signal handler failure");
            debug!("Ctrl-C handler: quitting the app");
            // quit(&app);
        });
        debug!("Ctrl-C handler has been set up successfully");

        app.run(|app, event| match event {
            // tauri::RunEvent::WindowEvent {
            //     label,
            //     event: win_event,
            //     ..
            // }
            //  => match win_event {
            //     tauri::WindowEvent::CloseRequested { api, .. } => {
            //         let minimize_to_tray = MINIMIZE_TO_TRAY.lock().unwrap();
            //         if *minimize_to_tray {
            //             let window = app.get_window(label.as_str()).unwrap();
            //             window.hide().unwrap();
            //             api.prevent_close();
            //         }
            //     }
            //     _ => {
            //     }
            // },
            // prevent shutdown on window close
            // tauri::RunEvent::ExitRequested { api, .. } => {
            //     debug!("Received exit request");
            //     api.prevent_exit();
            // },
             // 新增退出事件处理
            tauri::RunEvent::Exit => {
                debug!("Application is exiting...");
                quit(app);
            },
            _ => {
                trace!("Received event: {event:?}");
            }
        })
}
